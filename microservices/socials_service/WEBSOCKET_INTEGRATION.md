# WebSocket Integration for Real-Time Messaging

## Overview

The socials service now includes comprehensive WebSocket integration for real-time messaging notifications. This allows the frontend to receive instant updates when messages are sent, received, or fail across all supported social media platforms (Facebook, Instagram, Twitter).

## Features

### ✅ Real-Time Message Events
- **Message Sending**: Notified when a message is being processed
- **Message Sent**: Notified when a message is successfully sent
- **Message Failed**: Notified when a message fails to send
- **Message Received**: Notified when incoming messages are received via webhooks
- **Connection Status**: Connection confirmation and status updates

### ✅ Enhanced Connection Management
- **Multi-User Support**: Multiple users per organization can connect simultaneously
- **Heartbeat Support**: Automatic ping/pong to detect dead connections
- **Graceful Error Handling**: Proper cleanup and reconnection support
- **Organization-based Broadcasting**: Messages sent to all users in an organization
- **User-specific Tracking**: Each user connection is tracked individually

### ✅ Standardized Event Format
All WebSocket messages follow a consistent structure:

```json
{
  "event": "message_sent",
  "data": {
    "message_id": "msg_123",
    "conversation_id": "conv_456",
    "platform": "facebook",
    "sender_id": "user_789",
    "recipient_id": "user_012",
    "message_text": "Hello world!",
    "message_type": "text",
    "status": "sent",
    "created_time": "2025-08-02T10:30:00Z"
  },
  "timestamp": "2025-08-02T10:30:00Z",
  "organization_id": "org_345",
  "correlation_id": "corr_678"
}
```

## WebSocket Endpoint

### Connection URL
```
ws://localhost:8001/ws/messages
```

### Authentication
The WebSocket endpoint requires authentication via query parameters:
- `token`: JWT authentication token
- `organisation_id`: Organization ID for the connection

Example connection URL:
```
ws://localhost:8001/ws/messages?token=your_jwt_token&organisation_id=your_org_id
```

## Event Types

### 1. Connection Status (`connection_status`)
Sent when the WebSocket connection is established.

```json
{
  "event": "connection_status",
  "data": {
    "status": "connected",
    "user_id": "user_123",
    "user_email": "<EMAIL>",
    "organization_id": "org_456"
  }
}
```

### 2. Message Sending (`message_sending`)
Sent when a message is being processed (before API call).

```json
{
  "event": "message_sending",
  "data": {
    "platform": "facebook",
    "recipient_id": "recipient_123",
    "message_text": "Hello!",
    "message_type": "text",
    "status": "pending"
  }
}
```

### 3. Message Sent (`message_sent`)
Sent when a message is successfully sent to the social media platform.

```json
{
  "event": "message_sent",
  "data": {
    "message_id": "fb_msg_789",
    "conversation_id": "conv_456",
    "platform": "facebook",
    "recipient_id": "recipient_123",
    "message_text": "Hello!",
    "message_type": "text",
    "status": "sent"
  }
}
```

### 4. Message Failed (`message_failed`)
Sent when a message fails to send.

```json
{
  "event": "message_failed",
  "data": {
    "platform": "facebook",
    "recipient_id": "recipient_123",
    "message_text": "Hello!",
    "message_type": "text",
    "status": "failed",
    "error_message": "Rate limit exceeded"
  }
}
```

### 5. Message Received (`message_received`)
Sent when an incoming message is received via webhooks.

```json
{
  "event": "message_received",
  "data": {
    "message_id": "incoming_msg_123",
    "conversation_id": "conv_456",
    "platform": "instagram",
    "sender_id": "sender_789",
    "message_text": "Hi there!",
    "message_type": "text",
    "status": "delivered",
    "is_echo": false,
    "attachments": [],
    "reply_to": null
  }
}
```

## Heartbeat Protocol

The WebSocket connection supports heartbeat messages to detect dead connections:

### Client to Server (Ping)
```json
{
  "type": "ping",
  "timestamp": "2025-08-02T10:30:00Z"
}
```

### Server to Client (Pong)
```json
{
  "type": "pong",
  "timestamp": "2025-08-02T10:30:00Z"
}
```

### Heartbeat
```json
{
  "type": "heartbeat",
  "timestamp": "2025-08-02T10:30:00Z"
}
```

### Heartbeat Acknowledgment
```json
{
  "type": "heartbeat_ack",
  "timestamp": "2025-08-02T10:30:00Z"
}
```

## Integration Points

### Message Sending Endpoints
The following endpoints now include WebSocket notifications:

1. **Facebook**: `POST /facebook/send_message`
2. **Instagram**: `POST /instagram/send-text-message`
3. **Twitter**: `POST /twitter/dm_conversations/with`

Each endpoint:
- Sends `message_sending` event before API call
- Sends `message_sent` event on success
- Sends `message_failed` event on error

### Webhook Handlers
Incoming messages from social platforms trigger `message_received` events via the existing webhook handlers.

## Frontend Implementation Example

```javascript
class SocialsWebSocket {
  constructor(token, organizationId) {
    this.token = token;
    this.organizationId = organizationId;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const url = `ws://localhost:8001/ws/messages?token=${this.token}&organisation_id=${this.organizationId}`;
    this.ws = new WebSocket(url);

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
      this.startHeartbeat();
    };

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.reconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  handleMessage(data) {
    switch (data.event) {
      case 'message_sent':
        this.onMessageSent(data.data);
        break;
      case 'message_failed':
        this.onMessageFailed(data.data);
        break;
      case 'message_received':
        this.onMessageReceived(data.data);
        break;
      // Handle other events...
    }
  }

  startHeartbeat() {
    setInterval(() => {
      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({
          type: 'heartbeat',
          timestamp: new Date().toISOString()
        }));
      }
    }, 30000); // Send heartbeat every 30 seconds
  }

  // Event handlers
  onMessageSent(data) {
    console.log('Message sent:', data);
    // Update UI to show message as sent
  }

  onMessageFailed(data) {
    console.error('Message failed:', data);
    // Show error notification
  }

  onMessageReceived(data) {
    console.log('Message received:', data);
    // Add message to conversation UI
  }
}
```

## Testing

Use the provided test script to validate the WebSocket integration:

```bash
cd microservices/socials_service
python test_websocket_integration.py
```

Make sure to update the authentication token and organization ID in the script before running.

## Multi-User Support

### How It Works
The WebSocket service now supports **multiple users per organization**:

- **Connection Storage**: `organization_id -> {user_id: websocket}`
- **Broadcasting**: Messages sent to ALL users in the organization
- **Individual Tracking**: Each user connection tracked separately
- **Automatic Cleanup**: Dead connections removed automatically

### Example Scenario
1. **User A** (org_123) connects → Stored as `active_connections["org_123"]["user_A"]`
2. **User B** (org_123) connects → Stored as `active_connections["org_123"]["user_B"]`
3. **Message sent** → Both User A and User B receive the notification
4. **User A disconnects** → Only User A's connection removed, User B stays connected

### Connection Management Functions
```python
# Get connected users for an organization
connected_users = get_connected_users("org_123")  # ["user_A", "user_B"]

# Get connection count
count = get_connection_count("org_123")  # 2

# Check if specific user is connected
is_connected = is_user_connected("org_123", "user_A")  # True
```

## Benefits

1. **Real-Time Updates**: Frontend receives instant notifications
2. **Multi-User Support**: Multiple team members can collaborate in real-time
3. **Better UX**: Users see message status in real-time
4. **Error Handling**: Immediate feedback on failed messages
5. **Scalable**: Organization-based connections support multi-tenancy
6. **Reliable**: Heartbeat protocol ensures connection health
7. **Standardized**: Consistent event format across all platforms
8. **Team Collaboration**: All team members see the same message events
