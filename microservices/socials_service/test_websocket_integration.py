#!/usr/bin/env python3
"""
Test script for WebSocket integration with message sending endpoints.

This script demonstrates how to connect to the WebSocket endpoint and 
listen for real-time message events.
"""

import asyncio
import json
import logging
from datetime import datetime

import websockets

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketTestClient:
    def __init__(self, websocket_url: str, auth_token: str, organization_id: str):
        self.websocket_url = websocket_url
        self.auth_token = auth_token
        self.organization_id = organization_id
        self.websocket = None
        
    async def connect(self):
        """Connect to the WebSocket endpoint"""
        try:
            # Add authentication parameters to the URL
            url_with_auth = f"{self.websocket_url}?token={self.auth_token}&organisation_id={self.organization_id}"
            
            logger.info(f"Connecting to WebSocket: {url_with_auth}")
            self.websocket = await websockets.connect(url_with_auth)
            logger.info("WebSocket connection established")
            
        except Exception as e:
            logger.error(f"Failed to connect to WebSocket: {e}")
            raise
    
    async def listen_for_messages(self):
        """Listen for incoming WebSocket messages"""
        if not self.websocket:
            raise RuntimeError("WebSocket not connected")
            
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError:
                    logger.warning(f"Received non-JSON message: {message}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error listening for messages: {e}")
    
    async def handle_message(self, data: dict):
        """Handle incoming WebSocket messages"""
        event_type = data.get("event")
        timestamp = data.get("timestamp")
        message_data = data.get("data", {})
        
        logger.info(f"Received event: {event_type} at {timestamp}")
        
        if event_type == "connection_status":
            logger.info(f"Connection status: {message_data.get('status')}")
            
        elif event_type == "message_sending":
            platform = message_data.get("platform")
            recipient = message_data.get("recipient_id")
            text = message_data.get("message_text", "")[:50]
            logger.info(f"📤 Sending message on {platform} to {recipient}: {text}...")
            
        elif event_type == "message_sent":
            platform = message_data.get("platform")
            message_id = message_data.get("message_id")
            recipient = message_data.get("recipient_id")
            text = message_data.get("message_text", "")[:50]
            logger.info(f"✅ Message sent on {platform} (ID: {message_id}) to {recipient}: {text}...")
            
        elif event_type == "message_failed":
            platform = message_data.get("platform")
            recipient = message_data.get("recipient_id")
            error = message_data.get("error_message")
            text = message_data.get("message_text", "")[:50]
            logger.error(f"❌ Message failed on {platform} to {recipient}: {text}... Error: {error}")
            
        elif event_type == "message_received":
            platform = message_data.get("platform")
            sender = message_data.get("sender_id")
            text = message_data.get("message_text", "")[:50]
            logger.info(f"📥 Message received on {platform} from {sender}: {text}...")
            
        else:
            logger.info(f"Unknown event type: {event_type}")
            logger.debug(f"Full message data: {json.dumps(data, indent=2)}")
    
    async def send_heartbeat(self):
        """Send periodic heartbeat to keep connection alive"""
        if not self.websocket:
            return
            
        try:
            heartbeat_message = {
                "type": "heartbeat",
                "timestamp": datetime.utcnow().isoformat()
            }
            await self.websocket.send(json.dumps(heartbeat_message))
            logger.debug("Heartbeat sent")
        except Exception as e:
            logger.error(f"Failed to send heartbeat: {e}")
    
    async def close(self):
        """Close the WebSocket connection"""
        if self.websocket:
            await self.websocket.close()
            logger.info("WebSocket connection closed")

async def main():
    """Main test function"""
    # Configuration - Update these values for your environment
    WEBSOCKET_URL = "ws://localhost:8001/ws/messages"
    AUTH_TOKEN = "your_jwt_token_here"  # Replace with actual JWT token
    ORGANIZATION_ID = "your_org_id_here"  # Replace with actual organization ID

    print(f"Testing WebSocket connection for organization: {ORGANIZATION_ID}")
    print("Note: Multiple users from the same organization can now connect simultaneously!")
    print()

    client = WebSocketTestClient(WEBSOCKET_URL, AUTH_TOKEN, ORGANIZATION_ID)
    
    try:
        # Connect to WebSocket
        await client.connect()
        
        # Start heartbeat task
        heartbeat_task = asyncio.create_task(
            periodic_heartbeat(client)
        )
        
        # Listen for messages
        logger.info("Listening for WebSocket messages... Press Ctrl+C to stop")
        await client.listen_for_messages()
        
    except KeyboardInterrupt:
        logger.info("Stopping WebSocket client...")
    except Exception as e:
        logger.error(f"Error in main: {e}")
    finally:
        await client.close()
        if 'heartbeat_task' in locals():
            heartbeat_task.cancel()

async def periodic_heartbeat(client: WebSocketTestClient, interval: int = 30):
    """Send periodic heartbeat messages"""
    while True:
        try:
            await asyncio.sleep(interval)
            await client.send_heartbeat()
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"Error in heartbeat: {e}")

if __name__ == "__main__":
    print("WebSocket Integration Test Client")
    print("=" * 40)
    print("This script connects to the socials service WebSocket endpoint")
    print("and listens for real-time message events.")
    print()
    print("Before running:")
    print("1. Update WEBSOCKET_URL, AUTH_TOKEN, and ORGANIZATION_ID in the script")
    print("2. Ensure the socials service is running")
    print("3. Try sending messages through the API endpoints to see events")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest client stopped.")
