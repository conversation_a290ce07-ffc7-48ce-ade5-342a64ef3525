import logging
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

from fastapi import WebSocket
from pydantic import BaseModel

logger = logging.getLogger(__name__)

# Mapping of organization_id to Dict[user_id, WebSocket connection]
active_connections: Dict[str, Dict[str, WebSocket]] = {}


class MessageEventType(str, Enum):
    """Standardized message event types for WebSocket notifications"""
    MESSAGE_SENT = "message_sent"
    MESSAGE_RECEIVED = "message_received"
    MESSAGE_FAILED = "message_failed"
    MESSAGE_SENDING = "message_sending"
    MESSAGE_DELIVERED = "message_delivered"
    CONVERSATION_UPDATED = "conversation_updated"
    CONNECTION_STATUS = "connection_status"


class MessageStatus(str, Enum):
    """Message status types"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    READ = "read"


class WebSocketMessage(BaseModel):
    """Standardized WebSocket message structure"""
    event: MessageEventType
    data: Dict[str, Any]
    timestamp: datetime
    organization_id: str
    correlation_id: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class MessageEventData(BaseModel):
    """Standardized message event data structure"""
    message_id: Optional[str] = None
    conversation_id: Optional[str] = None
    platform: str
    sender_id: Optional[str] = None
    recipient_id: Optional[str] = None
    message_text: Optional[str] = None
    message_type: str = "text"  # text, image, video, audio, file, sticker
    status: MessageStatus
    error_message: Optional[str] = None
    attachments: Optional[list] = None
    reply_to: Optional[str] = None
    is_echo: bool = False
    created_time: Optional[datetime] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


# Register a new WebSocket connection for a user in an organization
def register_connection(organization_id: str, user_id: str, websocket: WebSocket):
    """Register a WebSocket connection for a user in an organization"""
    if organization_id not in active_connections:
        active_connections[organization_id] = {}

    active_connections[organization_id][user_id] = websocket
    logger.info(
        f"WebSocket connection registered for user {user_id} in "
        f"organization: {organization_id}"
    )


# Unregister a WebSocket connection for a user
def unregister_connection(organization_id: str, user_id: str):
    """Unregister a WebSocket connection for a user in an organization"""
    if organization_id in active_connections:
        if user_id in active_connections[organization_id]:
            active_connections[organization_id].pop(user_id, None)
            logger.info(
                f"WebSocket connection unregistered for user {user_id} in "
                f"organization: {organization_id}"
            )

            # Clean up empty organization entries
            if not active_connections[organization_id]:
                active_connections.pop(organization_id, None)
                logger.info(f"Removed empty organization entry: {organization_id}")


# Unregister all connections for an organization
def unregister_organization(organization_id: str):
    """Unregister all WebSocket connections for an organization"""
    if organization_id in active_connections:
        user_count = len(active_connections[organization_id])
        active_connections.pop(organization_id, None)
        logger.info(
            f"All WebSocket connections ({user_count}) unregistered for "
            f"organization: {organization_id}"
        )


# Utility functions
def get_connected_users(organization_id: str) -> List[str]:
    """Get list of connected user IDs for an organization"""
    if organization_id in active_connections:
        return list(active_connections[organization_id].keys())
    return []


def get_connection_count(organization_id: str) -> int:
    """Get the number of connected users for an organization"""
    if organization_id in active_connections:
        return len(active_connections[organization_id])
    return 0


def is_user_connected(organization_id: str, user_id: str) -> bool:
    """Check if a specific user is connected"""
    if organization_id in active_connections:
        return user_id in active_connections[organization_id]
    return False


async def is_connection_alive(websocket: WebSocket) -> bool:
    """Check if WebSocket connection is still alive"""
    try:
        await websocket.ping()
        return True
    except Exception:
        return False


async def send_websocket_message(
    organization_id: str,
    event: MessageEventType,
    data: Dict[str, Any],
    correlation_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Send a standardized WebSocket message to all users in an organization

    Args:
        organization_id: The organization ID to send the message to
        event: The type of event
        data: The event data
        correlation_id: Optional correlation ID for tracking

    Returns:
        Dict with status information
    """
    if organization_id not in active_connections:
        logger.warning(
            f"No active WebSocket connections for organization: {organization_id}"
        )
        return {"status": "no_connection", "organization_id": organization_id}

    org_connections = active_connections[organization_id]
    if not org_connections:
        logger.warning(
            f"No active users connected for organization: {organization_id}"
        )
        return {"status": "no_users", "organization_id": organization_id}

    message = WebSocketMessage(
        event=event,
        data=data,
        timestamp=datetime.now(timezone.utc),
        organization_id=organization_id,
        correlation_id=correlation_id
    )

    delivered_count = 0
    failed_users = []
    dead_connections = []

    # Send to all users in the organization
    for user_id, websocket in org_connections.items():
        try:
            # Check if connection is still alive
            if not await is_connection_alive(websocket):
                dead_connections.append(user_id)
                continue

            await websocket.send_json(message.dict())
            delivered_count += 1
            logger.debug(f"Message sent to user {user_id} in org {organization_id}")

        except Exception as e:
            logger.error(
                f"Failed to send message to user {user_id} in org "
                f"{organization_id}: {str(e)}"
            )
            failed_users.append(user_id)
            dead_connections.append(user_id)

    # Clean up dead connections
    for user_id in dead_connections:
        unregister_connection(organization_id, user_id)

    logger.info(
        f"WebSocket message '{event}' sent to {delivered_count} users in "
        f"organization {organization_id}"
    )

    if delivered_count > 0:
        return {
            "status": "delivered",
            "organization_id": organization_id,
            "delivered_count": delivered_count,
            "failed_users": failed_users
        }
    else:
        return {
            "status": "all_failed",
            "organization_id": organization_id,
            "failed_users": failed_users
        }


# Convenience functions for specific message types
async def publish_message_sent(
    organization_id: str,
    message_id: str,
    conversation_id: str,
    platform: str,
    recipient_id: str,
    message_text: str,
    message_type: str = "text",
    correlation_id: Optional[str] = None
) -> Dict[str, Any]:
    """Publish a message sent event"""
    event_data = MessageEventData(
        message_id=message_id,
        conversation_id=conversation_id,
        platform=platform,
        recipient_id=recipient_id,
        message_text=message_text,
        message_type=message_type,
        status=MessageStatus.SENT,
        created_time=datetime.now(timezone.utc)
    )

    return await send_websocket_message(
        organization_id=organization_id,
        event=MessageEventType.MESSAGE_SENT,
        data=event_data.dict(),
        correlation_id=correlation_id
    )


async def publish_message_failed(
    organization_id: str,
    platform: str,
    recipient_id: str,
    message_text: str,
    error_message: str,
    message_type: str = "text",
    correlation_id: Optional[str] = None
) -> Dict[str, Any]:
    """Publish a message failed event"""
    event_data = MessageEventData(
        platform=platform,
        recipient_id=recipient_id,
        message_text=message_text,
        message_type=message_type,
        status=MessageStatus.FAILED,
        error_message=error_message,
        created_time=datetime.now(timezone.utc)
    )

    return await send_websocket_message(
        organization_id=organization_id,
        event=MessageEventType.MESSAGE_FAILED,
        data=event_data.dict(),
        correlation_id=correlation_id
    )


async def publish_message_sending(
    organization_id: str,
    platform: str,
    recipient_id: str,
    message_text: str,
    message_type: str = "text",
    correlation_id: Optional[str] = None
) -> Dict[str, Any]:
    """Publish a message sending event (when message is being processed)"""
    event_data = MessageEventData(
        platform=platform,
        recipient_id=recipient_id,
        message_text=message_text,
        message_type=message_type,
        status=MessageStatus.PENDING,
        created_time=datetime.now(timezone.utc)
    )

    return await send_websocket_message(
        organization_id=organization_id,
        event=MessageEventType.MESSAGE_SENDING,
        data=event_data.dict(),
        correlation_id=correlation_id
    )


# Legacy function for backward compatibility
async def publish_new_message(user_id: str, message_data: dict):
    """
    Legacy function for backward compatibility with existing webhook handlers
    """
    # Convert legacy format to new standardized format
    event_data = MessageEventData(
        message_id=message_data.get("message_id"),
        conversation_id=message_data.get("conversation_id"),
        platform=message_data.get("platform", "unknown"),
        sender_id=message_data.get("sender"),
        recipient_id=message_data.get("recipient"),
        message_text=message_data.get("message"),
        status=MessageStatus.DELIVERED,
        attachments=message_data.get("attachments"),
        reply_to=message_data.get("reply_to"),
        is_echo=message_data.get("is_echo", False),
        created_time=datetime.fromisoformat(message_data["created_time"])
        if message_data.get("created_time") else datetime.now(timezone.utc)
    )

    return await send_websocket_message(
        organization_id=user_id,  # user_id is actually organization_id
        event=MessageEventType.MESSAGE_RECEIVED,
        data=event_data.dict()
    )
