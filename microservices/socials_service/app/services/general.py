from datetime import datetime, timezone
from app.database.session import SessionLocal
from app.utils.success_response import fail_response
from app.models.model import Comment, Conversation, Message, SocialMediaAccount
from app.services.websocket import publish_new_message
from app.services.facebook import (
    get_conversations_from_api as get_conversations_from_facebook_api)
from app.services.instagram import (
    get_conversations_from_api as get_conversations_from_instagram_api)
from app.utils.logger import get_logger

from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

logger = get_logger(__name__)


async def get_social_details_from_participant_ids(
    sender_id: str, recipient_id: str, db_session: AsyncSession
) -> SocialMediaAccount | None:
    result = await db_session.execute(
        select(SocialMediaAccount).where(
            or_(
                SocialMediaAccount.social_media_user_id.in_([sender_id, recipient_id]),
                SocialMediaAccount.page_id.in_([sender_id, recipient_id])
            )
        )
    )
    social_media_details = result.scalars().first()
    if not social_media_details:
        logger.info("social credentials not found")
        return None

    logger.info("social credentials found")
    return social_media_details


async def handle_comment_change(hook_data, db_session):
    data = hook_data.get("changes", [])[0].get("value", {})
    new_comment = Comment(
        sender=data.get("from"),
        content=data.get("text"),
        media=data.get("media"),
        post_id=data.get("media", {}).get("id"),
        parent_id=data.get("parent_id"),
        comment_id=data.get("id"),
        created_time=datetime.fromtimestamp(hook_data.get("time"))
    )
    await db_session.merge(new_comment)
    await db_session.commit()
    logger.info("saved comment to db")


async def handle_messaging_change(hook_data, platform, db_session):
    message_data = hook_data.get("messaging", [])[0]
    sender_id = message_data.get("sender", {})
    recipient_id = message_data.get("recipient", {})
    message_id = message_data.get("message", {}).get("mid")
    message_text = message_data.get("message", {}).get("text")
    created_at = message_data.get("timestamp")
    attachments = message_data.get("message", {}).get("attachments", [])
    reply_to = message_data.get("message", {}).get("reply_to", {}).get("mid") or message_data.get("message", {}).get("reply_to", {}).get("story", {}).get("url")
    is_echo = message_data.get("message", {}).get("is_echo")

    # add it to the message model
    logger.info("fetching the associated conversation")
    query = (
        select(Conversation)
        .where(
            and_(
                Conversation.participants.contains([{"id": sender_id.get("id")}]),
                Conversation.participants.contains([{"id": recipient_id.get("id")}]),
            )
        )
    )
    result = await db_session.execute(query)
    conversation = result.scalars().first()

    social_media_details: SocialMediaAccount | None = await get_social_details_from_participant_ids(
            sender_id.get("id"),
            recipient_id.get("id"),
            db_session
        )
    if not conversation:
        logger.info("No matching conversation found. Refetching conversations...")
        # fetch conversations from API and update db

        if social_media_details:
            if platform != "instagram":
                await get_conversations_from_facebook_api(
                    db_session=db_session,
                    social_account=social_media_details
                )
            else:
                await get_conversations_from_instagram_api(
                    db_session=db_session,
                    social_account=social_media_details
                )

            # re-run the convo query
            result = await db_session.execute(query)
            conversation = result.scalars().first()
            logger.info(f"Retried conversation fetch: {conversation.convo_id if conversation else 'None'}")
    if conversation:
        convo_id = conversation.convo_id
        participants = conversation.participants or []

        #  Update sender and recipient username based on participants
        for participant in participants:
            if participant.get("id") == sender_id.get("id"):
                sender_id["username"] = participant.get("username") or participant.get("name", "")
            if participant.get("id") == recipient_id.get("id"):
                recipient_id["username"] = participant.get("username") or participant.get("name", "")

        conversation.updated_time = datetime.now(timezone.utc)

        if platform == "instagram":
            new_message = Message(
                platform="instagram",
                sender=sender_id,
                recipient=recipient_id,
                message_id=message_id,
                message=message_text,
                created_time=datetime.fromtimestamp(created_at / 1000),
                reply_to=reply_to,
                attachments=attachments,
                is_echo=is_echo,
                conversation_id=convo_id
            )
        elif platform == "page":
            new_message = Message(
                platform="facebook",
                sender=sender_id,
                recipient=recipient_id,
                message_id=message_id,
                message=message_text,
                created_time=datetime.fromtimestamp(created_at / 1000),
                reply_to=reply_to,
                attachments=attachments,
                is_echo=is_echo,
                conversation_id=convo_id
            )
        else:
            return

        await db_session.merge(new_message)

        await db_session.commit()
        logger.info("saved message to db")
        # Send the new message to the frontend via WebSocket
        message_payload = {
            "platform": new_message.platform,
            "sender": new_message.sender,
            "recipient": new_message.recipient,
            "message_id": new_message.message_id,
            "message": new_message.message,
            "created_time": new_message.created_time.isoformat(),
            "reply_to": new_message.reply_to,
            "attachments": new_message.attachments,
            "is_echo": new_message.is_echo,
            "conversation_id": new_message.conversation_id,
        }
        await publish_new_message(
            social_media_details.organisation_id,
            message_payload
        )


async def process_webhook_message(data: dict):
    async with SessionLocal() as db_session:
        try:
            platform = data.get("object")
            hook_data = data.get("entry", [])[0]
            if hook_data.get("changes"):
                await handle_comment_change(hook_data, db_session)
            elif hook_data.get("messaging"):
                await handle_messaging_change(hook_data, platform, db_session)
            else:
                logger.warning("Unknown webhook format")
        except Exception as e:
            logger.error(f"error: {str(e)}")
            await db_session.rollback()
            return fail_response(500, str(e))
    return


def timestamp():
    now = datetime.now()
    timestamp_secs = now.timestamp()
    return int(timestamp_secs * 1000)


async def save_to_db(data):
    """save IG message to db"""
    # build a websocket framework

    response = {
        'object': 'instagram',
        'entry':
            [
                {
                    'messaging': [
                        {
                            'sender': {'id': data.get('sender_id')},
                            'recipient': {'id': data.get("recipient_id")},
                            'timestamp': timestamp(),
                            'message': {
                                'mid': data.get("message_id"),
                                'text': data.get("message")
                                }
                        }
                    ]
                }
            ]
    }
    await process_webhook_message(response)
    return
