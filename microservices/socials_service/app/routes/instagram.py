import asyncio
import traceback
from datetime import datetime
from typing import Annotated, List, Optional
import uuid

import httpx
import psycopg2
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from fastapi.responses import PlainTextResponse, RedirectResponse
from sqlalchemy import desc, func, or_, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.database.session import get_db
from app.models.model import (
    Comment,
    Conversation,
    MediaMetrics,
    Message,
    Post,
    SocialMediaAccount,
)
from app.routes.facebook import save_post_to_database
from app.schemas.instagram_schema import (
    CarouselMediaInfo,
    CommentReply,
    CommentResponse,
    ConversationResponse,
    GetConversationWithUserResponse,
    InitialiseInstagram,
    InstagramAccountReachTrendPoint,
    InstagramAudienceDemographicsSchema,
    InstagramAudienceGrowthTrendPoint,
    InstagramCarouselCreate,
    InstagramCarouselPostResponse,
    InstagramComment,
    InstagramCommentCreate,
    InstagramCommentResponse,
    InstagramCreate,
    InstagramEngagementTrendPoint,
    InstagramMediaMessage,
    InstagramMessageResponse,
    InstagramOverviewMetricsSchema,
    InstagramPostEngagementTrendPoint,
    InstagramSinglePostResponse,
    InstagramStickerResponse,
    InstagramTextMessage,
    InstagramTopPerformingPostSchema,
    MediaInfo,
    MediaObject,
    PostStatus,
    ScalarMetric,
)
from app.services.general import process_webhook_message, save_to_db
from app.services.instagram import (
    create_media_container,
    fetch_conversations_and_messages,
    get_instagram_account_reach_trend,
    get_instagram_audience_demographics,
    get_instagram_audience_growth_trend,
    get_instagram_engagement_trend,
    get_instagram_overview_metrics,
    get_instagram_post_engagement_trend,
    get_instagram_top_performing_posts,
    make_instagram_api_request,
    publish_media,
    save_comments_to_db,
)

from app.services.websocket import (
    publish_message_failed,
    publish_message_sending,
    publish_message_sent,
)
from app.utils.dependency import (
    check_permissions,
    create_JWT_Response,
    get_current_user,
    get_social_details,
    verify_organization,
)
from app.utils.logger import get_logger
from app.utils.success_response import fail_response, success_response

# Unified comment imports

logger = get_logger(__name__)
timeout = httpx.Timeout(20.0, connect=20.0, read=20.0, write=20.0)

router = APIRouter()


@router.get("/login")
async def instagram_login_with_instagram():
    """Redirect user to Instagram for login."""
    logger.info("Redirecting user to Instagram for login")

    return {"success": True, "url": settings.INSTAGRAM_EMBED_URL}


@router.get("/callback")
async def instagram_auth_callback(request: Request):
    logger.info("Received callback from Instagram")

    code = request.query_params.get("code").strip("#_")
    error = request.query_params.get("error")
    error_description = request.query_params.get("error_description")
    error_reason = request.query_params.get("error_reason")

    if error:
        logger.error(f"Authentication cancelled: {error_reason} - {error_description}")
        redirect_url = (
            f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
            f"error={error} {error_description}&social_type=instagram"
        )
        return RedirectResponse(
            url=redirect_url,
            status_code=status.HTTP_302_FOUND
        )

    if not code:
        logger.error("Missing 'code' parameter")
        redirect_url = (
            f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
            f"error={error} {error_description}&social_type=instagram"
        )
        return RedirectResponse(
            url=redirect_url,
            status_code=status.HTTP_302_FOUND
        )

    try:
        logger.info("Exchanging the code for token")
        async with httpx.AsyncClient(timeout=timeout) as client:
            token_response = await client.post(
                "https://api.instagram.com/oauth/access_token",
                data={
                    "client_id": settings.INSTAGRAM_APP_ID,
                    "client_secret": settings.INSTAGRAM_APP_SECRET,
                    "grant_type": "authorization_code",
                    "redirect_uri": settings.INSTAGRAM_REDIRECT_URL,
                    "code": code,
                },
            )
            token_data = token_response.json()
            if token_response.status_code != 200:
                redirect_url = (
                    f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
                    f"error={token_data.get('error_message')}&"
                    f"social_type=instagram"
                )
                return RedirectResponse(
                    url=redirect_url,
                    status_code=status.HTTP_302_FOUND
                )

            # get the short lived token
            response = {
                "access_token": token_data.get("access_token"),
                "user_id": token_data.get("user_id"),
            }

            # get the long lived token

            lng_token_response = await client.get(
                "https://graph.instagram.com/access_token",
                params={
                    "grant_type": "ig_exchange_token",
                    "client_secret": settings.INSTAGRAM_APP_SECRET,
                    "access_token": response.get("access_token"),
                }
            )
            lng_token_response.raise_for_status()

            lng_token_data = lng_token_response.json()

            response_data = {
                "access_token": lng_token_data.get("access_token"),
                "user_id": lng_token_data.get("user_id"),
                "expires_in": lng_token_data.get("expires_in"),
            }

            # get the instagram user details
            user_info_response = await client.get(
                f"{settings.INSTAGRAM_GRAPH_API_URL}/me",
                params={
                    "access_token": response_data.get("access_token"),
                    "fields": "user_id,username,name,account_type,followers_count,follows_count,media_count,profile_picture_url",
                },
            )
            user_info_response.raise_for_status()
            user_info = user_info_response.json()
            logger.info(f"Received user info: {user_info}")

            instagram_details = {
                "user_id": user_info.get("user_id"),
                "username": user_info.get("username"),
                "name": user_info.get("name"),
                "account_type": user_info.get("account_type"),
                "followers_count": user_info.get("followers_count"),
                "follows_count": user_info.get("follows_count"),
                "media_count": user_info.get("media_count"),
                "profile_picture_url": user_info.get("profile_picture_url"),
            }

        aggregated_data = {
            "access_token": response_data.get("access_token"),
            "expires_in": response_data.get("expires_in"),
            "social_type": "instagram",
            "instagram_details": instagram_details,
        }

        logger.info("Returning successful response")
        # get the JWT encoded version
        encoded_response = await create_JWT_Response(aggregated_data)
        return RedirectResponse(
            url=f"{settings.FRONTEND_SOCIALS_ENDPOINT}?token={encoded_response}",
            status_code=status.HTTP_302_FOUND
        )
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during authentication: {str(e)}")
        redirect_url = (
            f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
            f"error={e}&"
            f"social_type=instagram"
        )
        return RedirectResponse(
            url=redirect_url,
            status_code=status.HTTP_302_FOUND
        )
    except Exception as e:
        logger.error(f"Unexpected error during authentication: {str(e)}")
        return fail_response(500, "An unexpected error occurred during authentication")


@router.post("/connect_instagram")
async def connect_instagram_to_backend(
    token: Annotated[dict, Depends(get_current_user)],
    user_details: InitialiseInstagram,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    try:
        await check_permissions(token.get('user_id'), organisation_id, 'can connect socials')

        # check if social is already linked
        result = await db_session.execute(
            select(SocialMediaAccount).filter_by(
                platform="instagram",
                social_media_user_id=user_details.social_media_user_id,
            )
        )
        existing_social = result.scalars().first()
        if existing_social:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="This social media account has already been linked to an organisation",
            )
        instagram_social_account = SocialMediaAccount(
            platform="instagram",
            username=user_details.username,
            social_media_user_id=user_details.social_media_user_id,
            organisation_id=organisation_id,
            access_token=user_details.access_token,
            login_status=True,
        )

        db_session.add(instagram_social_account)
        await db_session.commit()
        await db_session.refresh(instagram_social_account)

        logger.info(
            f"Created social media account: {instagram_social_account.platform} -- {instagram_social_account.username}"
        )

        # Schedule immediate metrics collection in the background
        try:
            logger.info(
                "running important startup tasks"
                "1. fetching metrics for the IG account: "
                f"{instagram_social_account.username}"
                "2. fetching convo n messages for the IG account:"
                f"{instagram_social_account.username}"
            )
            asyncio.create_task(
                get_instagram_overview_metrics(
                    organisation_id=organisation_id,
                    db_session=db_session)
            )
            asyncio.create_task(
                fetch_conversations_and_messages(
                    instagram_social_account))

        except Exception as e:
            logger.error(f"Error updating metrics for Instagram account ID {instagram_social_account.id}: {str(e)}")

        return success_response(
            200,
            "Social media account created successfully",
            {
                "username": instagram_social_account.username,
                "organisation_id": instagram_social_account.organisation_id,
                "social_media_user_id": instagram_social_account.social_media_user_id,
                "platform": instagram_social_account.platform,
                "login_status": instagram_social_account.login_status,
            },
        )
    except IntegrityError as e:
        if isinstance(e.orig, psycopg2.errors.UniqueViolation):
            await db_session.rollback()
            logger.error(f"UniqueViolationError: {str(e)}")
            return fail_response(409, "This social media account has already been linked to an organisation")
        elif getattr(e.orig, 'pgcode', None) == '23505':
            await db_session.rollback()
            logger.error(f"UniqueViolationError: {str(e)}")
            return fail_response(409, "This social media account has already been linked to an organisation")
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/media", response_model=List[MediaObject])
async def get_media_objects(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Fetches the media objects for the given Instagram user ID.
    """
    # check the social profile exists
    db_social = await get_social_details(
        organisation_id, "instagram", db_session
    )

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{settings.INSTAGRAM_API_BASE_URL}/me/media",
                params={
                    "access_token": db_social.access_token,
                    "fields": "alt_text,comments_count,id,like_count,media_type,media_url,owner,permalink,thumbnail_url,timestamp,username",
                }
            )
            response.raise_for_status()
            data = response.json()

        return data.get('data', [])
    except httpx.HTTPError as e:
        raise HTTPException(status_code=400, detail=f"HTTP error occurred: {str(e)}")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")


@router.post("/post", response_model=InstagramSinglePostResponse)
async def create_ig_single_post(
    data: InstagramCreate,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
    schedule_content_id: str = '',
) -> InstagramSinglePostResponse:
    """
    Creates a media container for single images/videos or carousel items.
    """
    try:
        # check the social profile exists
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="instagram",
            db_session=db_session
        )

        logger.info(f"Creating single Instagram post with media_type: {data.media_type}")

        container_id = await create_media_container(
            data=data,
            access_token=db_social.access_token
        )

        if not container_id:
            raise HTTPException(
                status_code=500,
                detail="Failed to create media container"
            )

        logger.info(f"Created container: {container_id}")

        media_id = await publish_media(container_id, db_social.access_token)
        logger.info(f"Published media with ID: {media_id}")

        # Create media info
        media_info = MediaInfo(
            media_type=data.media_type,
            url=data.image_url or data.video_url,
            container_id=container_id
        )

        await save_post_to_database(
            post_id=media_id,
            schedule_content_id=schedule_content_id,
            social_media_account_id=db_social.id,
            db_session=db_session
        )

        return InstagramSinglePostResponse(
            container_id=container_id,
            media_id=media_id,
            media_info=media_info,
            status=PostStatus.PUBLISHED,
            created_at=datetime.now().isoformat() + "Z"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating single Instagram post: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/post/carousel", response_model=InstagramCarouselPostResponse)
async def create_carousel_container(
    organisation_id: Annotated[str, Depends(verify_organization)],
    data: InstagramCarouselCreate,
    db_session: AsyncSession = Depends(get_db),
    schedule_content_id: str = '',
) -> InstagramCarouselPostResponse:
    """
    Creates a carousel container for multiple images/videos.
    """
    try:
        # check the social profile exists
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="instagram",
            db_session=db_session
        )

        logger.info(f"Creating carousel with {len(data.medias)} media items")

        # Validate carousel data
        if not data.medias:
            raise HTTPException(
                status_code=400,
                detail="At least one media item is required for carousel"
            )

        if len(data.medias) > 10:
            raise HTTPException(
                status_code=400,
                detail="Maximum 10 media items allowed in carousel"
            )

        # Create individual media containers
        container_ids = []
        for i, media in enumerate(data.medias):
            logger.info(f"Creating container for media item {i+1}/{len(data.medias)}")
            try:
                container_id = await create_media_container(
                    media, db_social.access_token
                )
                if not container_id:
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to create container for media item {i+1}"
                    )
                container_ids.append(container_id)
                logger.info(f"Created container {container_id} for media item {i+1}")
            except Exception as e:
                logger.error(f"Error creating container for media item {i+1}: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to create container for media item {i+1}: {str(e)}"
                )

        # Create carousel container
        url = f"{settings.INSTAGRAM_API_BASE_URL}/me/media"
        headers = {
            "Authorization": f"Bearer {db_social.access_token}",
            "Content-Type": "application/json"
        }
        payload = {
            "media_type": "CAROUSEL",
            "children": ",".join(container_ids)
        }
        if data.caption:
            payload["caption"] = data.caption

        logger.info(f"Creating carousel container with children: {payload['children']}")

        response_data, error = await make_instagram_api_request(
            "POST", url, headers, payload
        )

        if error:
            logger.error(f"Error creating carousel container: {str(error)}")
            raise HTTPException(status_code=500, detail=str(error))

        if not response_data or "id" not in response_data:
            raise HTTPException(
                status_code=500,
                detail="Failed to create carousel container - no ID returned"
            )

        carousel_container_id = response_data["id"]
        logger.info(f"Created carousel container: {carousel_container_id}")

        # Publish the carousel
        try:
            media_id = await publish_media(
                creation_id=carousel_container_id,
                access_token=db_social.access_token
            )
            logger.info(f"Published carousel with media ID: {media_id}")

            # Create media info for each item
            media_items = []
            for i, media in enumerate(data.medias):
                media_info = MediaInfo(
                    media_type=media.media_type,
                    url=media.image_url or media.video_url,
                    container_id=container_ids[i]
                )
                media_items.append(media_info)

            carousel_info = CarouselMediaInfo(
                total_items=len(container_ids),
                media_items=media_items
            )

            await save_post_to_database(
                post_id=media_id,
                schedule_content_id=schedule_content_id,
                social_media_account_id=db_social.id,
                db_session=db_session
            )

            return InstagramCarouselPostResponse(
                container_id=carousel_container_id,
                media_id=media_id,
                carousel_info=carousel_info,
                caption=data.caption,
                status=PostStatus.PUBLISHED,
                created_at=datetime.now().isoformat() + "Z"
            )
        except Exception as e:
            logger.error(f"Error publishing carousel: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Carousel container created but failed to publish: {str(e)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in create_carousel_container: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/check_publishing_status")
async def check_publishing_status(
    container_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Checks the publishing status of a media container.
    """
    # check the social profile exists
    db_social = await get_social_details(
        organisation_id=organisation_id,
        platform_name="instagram",
        db_session=db_session
    )
    url = f"{settings.INSTAGRAM_API_BASE_URL}/{container_id}"
    headers = {"Content-Type": "application/json"}
    params = {
        "fields": "status_code,id",
        "access_token": db_social.access_token,
    }

    try:
        response_data, error = await make_instagram_api_request("GET", url, headers, params)

        if error:
            raise HTTPException(status_code=500, detail=str(error))

        if not response_data:
            raise HTTPException(
                status_code=500,
                detail="Failed to get publishing status - no data returned"
            )

        return {
            "status_code": response_data.get("status_code"),
            "id": response_data.get("id")
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/check_publishing_limit")
async def check_publishing_limit(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Checks the publishing rate limit for an Instagram professional account.
    """
    # check the social profile exists
    db_social = await get_social_details(
        organisation_id=organisation_id,
        platform_name="instagram",
        db_session=db_session
    )
    url = f"{settings.INSTAGRAM_API_BASE_URL}/{db_social.social_media_user_id}/content_publishing_limit"
    headers = {"Content-Type": "application/json"}
    params = {
        "access_token": db_social.access_token,
        "fields": "config,quota_usage"
    }

    try:
        response_data, _ = await make_instagram_api_request("GET", url, headers, params)
        return response_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{scheduled_content_id}/comment", response_model=CommentResponse)
async def create_comment(
    data: InstagramCommentCreate,
    scheduled_content_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Creates a comment on a specific Instagram media object.
    """
    # check the social profile exists
    db_social = await get_social_details(
        organisation_id=organisation_id,
        platform_name="instagram",
        db_session=db_session
    )
    # get the required post id
    result = await db_session.execute(
        select(Post).where(
            Post.schedulecontent_id == scheduled_content_id,
            Post.social_media_account_id == db_social.id
        )
    )
    post = result.scalars().first()
    if not post:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail='Requested content not found'
        )
    url = f"{settings.INSTAGRAM_API_BASE_URL}/{post.post_id}/comments"
    headers = {"Content-Type": "application/json"}
    payload = {
        "message": data.message,
        "access_token": db_social.access_token,
    }

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()
            response_data = response.json()
        return response_data
    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=e.response.status_code, detail=e.response.text)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        return fail_response(500, str(e))


# @router.get("/{scheduled_content_id}")
async def save_comment_to_db(
    scheduled_content_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    try:
        # check the social profile exists
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="instagram",
            db_session=db_session
        )
        # get the required post id
        result = await db_session.execute(
            select(Post).where(
                Post.schedulecontent_id == scheduled_content_id,
                Post.social_media_account_id == db_social.id
            )
        )
        post = result.scalars().first()
        if not post:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail='Requested content not found'
            )
        response = await save_comments_to_db(post.post_id, db_social.access_token, db_session)
        return response
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        return fail_response(500, str(e))


@router.get("/{scheduled_content_id}/comment", response_model=InstagramCommentResponse)
async def get_comments(
    scheduled_content_id: str,
    _: Annotated[str, Depends(verify_organization)],
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db_session: AsyncSession = Depends(get_db)
):
    """
    Gets all comments on a specific Instagram media object.
    """
    try:
        # get the required post id
        result = await db_session.execute(
            select(Post).where(
                Post.schedulecontent_id == scheduled_content_id
            )
        )
        post = result.scalars().first()
        if not post:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail='Requested content not found'
            )
        # Get total count for pagination metadata
        total = await db_session.scalar(
            select(func.count()).where(
                Comment.post_id == post.post_id,
                Comment.parent_id.is_(None),
                or_(
                    Comment.extra_data['hidden'].as_boolean() is not False,
                    Comment.extra_data['hidden'] is None
                )
            )
        )
        result = await db_session.execute(
            select(Comment)
            .where(
                Comment.post_id == post.post_id,
                Comment.parent_id.is_(None),
                or_(
                    Comment.extra_data['hidden'].as_boolean() is not False,
                    Comment.extra_data['hidden'] is None
                )
            )
            .order_by(Comment.created_time.desc())
            .offset(offset)
            .limit(limit)
        )
        comments = result.scalars().all()
        return {
            "total": total,
            "limit": limit,
            "offset": offset,
            "comments": comments
            }
    except HTTPException as e:
        raise e
    except Exception as e:
        return fail_response(500, str(e))


@router.post("/reply_to_comment", response_model=CommentResponse)
async def reply_to_comment(
    message: CommentReply,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Replies to a specific comment on Instagram.
    """
    # check the social profile exists
    db_social = await get_social_details(
        organisation_id=organisation_id,
        platform_name="instagram",
        db_session=db_session
    )
    url = f"{settings.INSTAGRAM_API_BASE_URL}/{message.comment_id}/replies"
    headers = {"Content-Type": "application/json"}
    payload = {
        "message": message.message,
        "access_token": db_social.access_token,
    }

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()
            response_data = response.json()
            return response_data
    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=e.response.status_code, detail=e.response.text)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        return fail_response(500, str(e))


@router.get("/comments/replies", response_model=List[InstagramComment])
async def get_comment_replies(
    ig_comment_id: str,
    _: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Get replies to a specific comment on Instagram.
    """

    try:
        request = await db_session.execute(
            select(Comment).where(
                Comment.parent_id == ig_comment_id,
                or_(
                    Comment.extra_data['hidden'].as_boolean() is False,
                    Comment.extra_data['hidden'] is None
                )
            )
        )
        comments = request.scalars().all()
        return comments
    except HTTPException as e:
        raise e
    except Exception as e:
        return fail_response(500, str(e))


@router.delete("/delete_comment")
async def delete_comment(
    ig_comment_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Deletes a specific comment on Instagram.
    """
    # check the social profile exists
    db_social = await get_social_details(
        organisation_id=organisation_id,
        platform_name="instagram",
        db_session=db_session
    )
    url = f"{settings.INSTAGRAM_API_BASE_URL}/{ig_comment_id}"
    headers = {"Content-Type": "application/json"}
    params = {
        "access_token": db_social.access_token,
    }

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.delete(url, headers=headers, params=params)
            # response.raise_for_status()
            response_data = response.json()
            logger.info(response_data)
            # remove it from the db
            result = await db_session.execute(
                select(Comment).where(
                    Comment.comment_id == ig_comment_id
                )
            )
            comment = result.scalars().first()
            await db_session.delete(comment)
            await db_session.commit()
            return {"message": "Comment deleted successfully"}
    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=e.response.status_code, detail=e.response.text)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        return fail_response(500, str(e))


@router.put("/toggle_comment_visibility")
async def toggle_comment_visibility(
    ig_comment_id: str,
    hide: bool,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Hides or unhides a specific comment on Instagram.
    """
    # check the social profile exists
    db_social = await get_social_details(
        organisation_id=organisation_id,
        platform_name="instagram",
        db_session=db_session
    )
    url = f"{settings.INSTAGRAM_API_BASE_URL}/{ig_comment_id}"
    headers = {"Content-Type": "application/json"}
    payload = {
        "hide": "true" if hide else "false",
        "access_token": db_social.access_token,
    }

    try:
        await make_instagram_api_request("POST", url, headers, data=payload)
        return {"message": "Comment visibility updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/toggle_media_comments")
async def toggle_media_comments(
    ig_media_id: str,
    disable: bool,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Disables or enables comments on a specific media object.
    """
    # check the social profile exists
    db_social = await get_social_details(
        organisation_id=organisation_id,
        platform_name="instagram",
        db_session=db_session
    )
    url = f"{settings.INSTAGRAM_API_BASE_URL}/{ig_media_id}"
    headers = {"Content-Type": "application/json"}
    payload = {
        "comment_enabled": "false" if disable else "true",
        "access_token": db_social.access_token,
    }

    try:
        await make_instagram_api_request("POST", url, headers, data=payload)
        return {"message": "Comment settings updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# INSIGHTS
@router.get("/{scheduled_content_id}/insights", response_model=List[ScalarMetric])
async def get_media_insights(
    scheduled_content_id: str,
    _: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    Gets insights (metrics) for a specific Instagram media object.
    """
    # get the required post id
    result = await db_session.execute(
        select(MediaMetrics)
        .where(
            MediaMetrics.scheduled_content_id == scheduled_content_id
        )
    )
    metrics = result.scalars().all()
    return metrics


@router.get("/account_insights")
async def get_account_insights(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
    period: str = "day"
):
    """
    Gets insights (metrics) for an Instagram professional account.

    Parameters:
    - organisation_id: The organization ID to fetch the account for
    - period: The period for metrics (day, week, month, etc.)

    Returns:
    - Account insights data with metrics formatted for easy consumption
    """
    try:
        # Validate input parameters
        valid_periods = ["day", "lifetime"]
        if period not in valid_periods:
            return fail_response(
                400,
                f"Invalid period. Must be one of: {', '.join(valid_periods)}"
            )

        # Check the social profile exists
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="instagram",
            db_session=db_session
        )
        # Set up API request - request all available metrics
        metrics = "views,accounts_engaged,comments,follows_and_unfollows,profile_links_taps,replies,saves,shares,total_interactions,likes"
        url = f"{settings.INSTAGRAM_API_BASE_URL}/me/insights"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {db_social.access_token}"
        }
        params = {
            "metric": metrics,
            "period": period,
        }

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                url,
                params=params,
                headers=headers,
            )
            response.raise_for_status()
            data = response.json()
        # Process and return successful response
        insights_data = data.get("data", [])
        logger.info(insights_data)
        formatted_insights = {}
        for item in insights_data:
            metric_name = item.get("name")
            # Handle different value formats
            values = item.get("values", [])
            if values:
                if isinstance(values[0], dict) and "value" in values[0]:
                    metric_value = values[0].get("value", 0)
                else:
                    metric_value = values[0] if values[0] is not None else 0
            else:
                metric_value = 0

            formatted_insights[metric_name] = metric_value

        # Add account info
        account_info = {
            "username": db_social.username,
            "account_id": db_social.social_media_user_id,
            "period": period
        }

        return success_response(
            200,
            "Account insights retrieved successfully",
            {
                "account_info": account_info,
                "raw_insights": insights_data,
                "metrics": formatted_insights
            }
        )

    except Exception as e:
        logger.error(f'an error occure: {str(e)}\ntraceback: {traceback.format_exc()}')
        return fail_response(500, "An unexpected error occurred while fetching account insights")


# @router.get("/demographics", response_model=DemographicsResponse)
async def get_demographics_insights(
    organisation_id: Annotated[str, Depends(verify_organization)],
    period: str = Query('day', description='period to fetch data from'),
    timeframe: str = Query('this_month', description='timeframe to fetch data from'),
    db_session: AsyncSession = Depends(get_db),
):
    """
    Gets demographics insights (metrics) for an Instagram professional account.
    Demographics is calculated for top 45 performers only.

    Returns a flattened format suitable for frontend consumption.
    """
    try:
        valid_period = ["day", "lifetime"]
        valid_timeframes = ["this_month", "this_week"]
        if period and period not in valid_period:
            raise ValueError(f'period can only be {", ".join(valid_period)}')
        if timeframe and timeframe not in valid_timeframes:
            raise ValueError(
                f'invalid metric type use one of {", ".join(valid_timeframes)}'
            )

        # Check the social profile exists
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="instagram",
            db_session=db_session
        )

        # Set up API request
        metrics = "engaged_audience_demographics,follower_demographics"
        url = f"{settings.INSTAGRAM_API_BASE_URL}/me/insights"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {db_social.access_token}"
        }
        params = {
            "metric": metrics,
            "period": period if period else "lifetime",
            # "timeframe": timeframe,
            "breakdown": "age,city,country,gender",
            "metric_type": "total_value"
        }

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                url,
                params=params,
                headers=headers,
            )
            print(response.json())
            response.raise_for_status()
            api_data = response.json()

        insights_data = api_data.get("data", [])
        simplified_metrics = []

        for item in insights_data:
            metric_name = item.get("name")
            period = item.get("period")
            total_value = item.get("total_value", {})
            breakdowns = total_value.get("breakdowns", [])

            # Flatten breakdown results
            for breakdown in breakdowns:
                keys = breakdown.get("dimension_keys", [])
                results = breakdown.get("results", [])

                # Detect timeframe (usually same across all)
                timeframe = results[0]["dimension_values"][0] if results else None

                flat_results = []
                for result in results:
                    dimension_values = result.get("dimension_values", [])
                    value = result.get("value", 0)

                    breakdown_data = {
                        "value": value
                    }

                    # Map dimension_keys to values
                    for i, key in enumerate(keys):
                        if key.lower() != "timeframe":
                            breakdown_data[key] = dimension_values[i]

                    flat_results.append(breakdown_data)

                simplified_metrics.append({
                    "metric": metric_name,
                    "period": period,
                    "timeframe": timeframe,
                    "data": flat_results
                })

        account_info = {
            "username": db_social.username,
            "account_id": db_social.social_media_user_id,
            "period": period if period else "lifetime"
        }

        return success_response(
            200,
            "Account insights retrieved successfully",
            {
                "account_info": account_info,
                "metrics": simplified_metrics
            }
        )

    except Exception as e:
        logger.error(
            f'an error occurred: {str(e)}\ntraceback: {traceback.format_exc()}'
        )
        return fail_response(
            500, "An unexpected error occurred while fetching account insights"
        )


# @router.get("/reach", response_model=ReachResponse)
async def get_reach_insights(
    organisation_id: str = Depends(verify_organization),
    period: str = Query("day", description="Metric period (day, week, etc.)"),
    since: Optional[int] = Query(None, description="Unix timestamp to start from"),
    until: Optional[int] = Query(None, description="Unix timestamp to end at"),
    after: Optional[str] = Query(None, description="Cursor for next page"),
    before: Optional[str] = Query(None, description="Cursor for previous page"),
    db_session: AsyncSession = Depends(get_db),
):
    """
    Gets 'reach' insights (daily breakdown by media_product_type),
    with paging via `next`/`previous` URLs.
    """
    try:
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="instagram",
            db_session=db_session,
        )

        url = f"{settings.INSTAGRAM_API_BASE_URL}/me/insights"
        params = {
            "metric": "reach",
            "period": period,
            "breakdown": "media_product_type",
            "metric_type": "total_value",
        }
        if since is not None:
            params["since"] = since
        if until is not None:
            params["until"] = until
        if after:
            params["after"] = after
        if before:
            params["before"] = before

        headers = {"Authorization": f"Bearer {db_social.access_token}"}

        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(url, params=params, headers=headers)
            resp.raise_for_status()
            payload = resp.json()

        # Facebook always returns a list under "data" with one item for this call
        item = payload["data"][0]
        total = item["total_value"].get("value", 0)

        breakdown = item["total_value"]["breakdowns"][0]["results"]
        data = [
            {
                "media_product_type": r["dimension_values"][0],
                "value": r["value"],
            }
            for r in breakdown
        ]

        paging = payload.get("paging", {})
        return success_response(
            200,
            "Reach metrics retrieved successfully",
            {
                "metric": {
                    "metric": "reach",
                    "period": item["period"],
                    "total": total,
                    "data": data,
                },
                "paging": {
                    "previous": paging.get("previous"),
                    "next": paging.get("next"),
                },
            },
        )

    except httpx.HTTPStatusError as e:
        return fail_response(e.response.status_code, e.response.text)
    except Exception as e:
        return fail_response(500, f"Unexpected error: {e}")


# Message API
@router.post("/send-text-message", response_model=InstagramMessageResponse)
async def send_text_message(
    message: InstagramTextMessage,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """Send a message that contains text or a link
    * recipient_id: the Instagram-scoped ID (<IGSID>)
    * text: the message parameter containing the text or link."""
    # Generate correlation ID for tracking
    correlation_id = str(uuid.uuid4())

    # Notify frontend that message is being sent
    await publish_message_sending(
        organization_id=organisation_id,
        platform="instagram",
        recipient_id=message.recipient.id,
        message_text=message.text,
        message_type="text",
        correlation_id=correlation_id
    )

    # check the social profile exists
    db_social = await get_social_details(
        organisation_id=organisation_id,
        platform_name="instagram",
        db_session=db_session
    )
    url = f"{settings.INSTAGRAM_API_BASE_URL}/me/messages"
    headers = {
        "Authorization": f"Bearer {db_social.access_token}",
        "Content-Type": "application/json"}
    payload = {
        "recipient": {"id": message.recipient.id},
        "message": {"text": message.text}}

    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                url, headers=headers, json=payload
            )
            response.raise_for_status()

            response_data = response.json()
            data = {
                "sender_id": db_social.social_media_user_id,
                "recipient_id": message.recipient.id,
                "message_id": response_data.get("message_id"),
                "message": message.text
            }
            await save_to_db(data)

            # Notify frontend of successful message send
            await publish_message_sent(
                organization_id=organisation_id,
                message_id=response_data.get("message_id"),
                conversation_id=message.recipient.id,
                platform="instagram",
                recipient_id=message.recipient.id,
                message_text=message.text,
                message_type="text",
                correlation_id=correlation_id
            )

        if response_data:
            return {
                "recipient": response_data.get("recipient_id"),
                "message_id": response_data.get("message_id"),
                "message": message.text,
                "sent_time": datetime.now()
            }
    except httpx.HTTPStatusError as e:
        error_response = e.response.json().get('error')
        error_msg = error_response.get('message') if error_response else 'Instagram API error'

        # Notify frontend of failed message send
        await publish_message_failed(
            organization_id=organisation_id,
            platform="instagram",
            recipient_id=message.recipient.id,
            message_text=message.text,
            error_message=error_msg,
            message_type="text",
            correlation_id=correlation_id
        )

        raise HTTPException(
            status_code=e.response.status_code, detail=error_msg)
    except HTTPException as e:
        # Notify frontend of failed message send
        await publish_message_failed(
            organization_id=organisation_id,
            platform="instagram",
            recipient_id=message.recipient.id,
            message_text=message.text,
            error_message=str(e.detail),
            message_type="text",
            correlation_id=correlation_id
        )
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        error_msg = 'an error occurred'
        logger.error(f'an error occured in sending text message: {str(e)}')

        # Notify frontend of failed message send
        await publish_message_failed(
            organization_id=organisation_id,
            platform="instagram",
            recipient_id=message.recipient.id,
            message_text=message.text,
            error_message=error_msg,
            message_type="text",
            correlation_id=correlation_id
        )

        return fail_response(500, error_msg)


@router.post("/send-media-message", response_model=InstagramMessageResponse)
async def send_media_message(
    message: InstagramMediaMessage,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """send an audio, image or video message
    * id: the recipient Instagram-scoped ID (<IGSID>)
    * media_type: as audio, image or video
    * url: set to the URL for the audio , image or video file."""
    # check the social profile exists
    db_social = await get_social_details(
        organisation_id=organisation_id,
        platform_name="instagram",
        db_session=db_session
    )
    url = f"{settings.INSTAGRAM_API_BASE_URL}/me/messages"
    headers = {
        "Authorization": f"Bearer {db_social.access_token}",
        "Content-Type": "application/json"
    }
    payload = {
        "recipient": {"id": message.recipient.id},
        "message": {
            "attachment": {
                "type": message.media_type,
                "payload": {"url": message.url}
            }
        },
    }
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                url, headers=headers, json=payload
            )
            response.raise_for_status()
            response_data = response.json()
        if response_data:
            return {
                "recipient": response_data.get("recipient_id"),
                "message_id": response_data.get("message_id"),
                "message": message.url,
                "sent_time": datetime.now()
            }
    except httpx.HTTPStatusError as e:
        response = e.response.json().get('error')
        raise HTTPException(
            status_code=e.response.status_code, detail=response.get('message'))
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f'an error occured in sending text message: {str(e)}')
        return fail_response(500, 'an error occurred')


@router.post("/send-sticker", response_model=InstagramMessageResponse)
async def send_heart_sticker(
    message: InstagramStickerResponse,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """
    ## Send a heart sticker
    * id: the recipient Instagram-scoped ID (<IGSID>)
    * message parameter containing an attachment object with the type set to ```like_heart```.
    """
    # check the social profile exists
    db_social = await get_social_details(
        organisation_id=organisation_id,
        platform_name="instagram",
        db_session=db_session
    )
    url = f"{settings.INSTAGRAM_API_BASE_URL}/me/messages"
    headers = {
        "Authorization": f"Bearer {db_social.access_token}",
        "Content-Type": "application/json"}
    payload = {
        "recipient": {"id": message.recipient.id},
        "message": {
            "attachment": {"type": message.sticker_type}
        },
    }
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                url, headers=headers, json=payload
            )
            response.raise_for_status()
            response_data = response.json()
            logger.info(response_data)
        if response_data:
            return {
                "recipient": response_data.get("recipient_id"),
                "message_id": response_data.get("message_id"),
                "message": message.sticker_type,
                "sent_time": datetime.now()
            }
    except httpx.HTTPStatusError as e:
        response = e.response.json().get('error')
        raise HTTPException(
            status_code=e.response.status_code, detail=response.get('message'))
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f'an error occured in sending text message: {str(e)}')
        return fail_response(500, 'an error occurred')


# conversations API
@router.get("/conversations", response_model=List[ConversationResponse])
async def get_conversations(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """get a list of your app user's conversations for an Instagram professional account"""
    try:
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="instagram",
            db_session=db_session
        )
        query = (
            select(Conversation)
            .where(Conversation.social_media_account_id == db_social.id)
            .order_by(desc(Conversation.updated_time))
        )
        result = await db_session.execute(query)
        conversations = result.scalars().all()
        return conversations
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f'A server error occurred: {str(e)}')
        return fail_response(500, "An unexpected error occurred")


@router.get("/conversations/{conversation_id}/messages", response_model=GetConversationWithUserResponse)
async def get_messages_in_conversation(
    conversation_id: str,
    _: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """get a list of earliest twenty messages in the db and saves it in the db"""
    query = (
        select(Message)
        .where(Message.conversation_id == conversation_id)
        .order_by(Message.created_time.desc())
        .limit(25)
    )
    result = await db_session.execute(query)
    messages = result.scalars().all()
    return messages


# WEBHOOK
@router.get('/webhook')
async def receive_webhook(request: Request):
    mode = request.query_params.get('hub.mode')
    verify_token = request.query_params.get('hub.verify_token')
    challenge = request.query_params.get('hub.challenge')
    logger.info(f"Webhook verification attempt: mode='{mode}', token='{verify_token}', challenge='{challenge}'")

    # Your verify token
    expected_verify_token = 'ellumai'
    if mode == 'subscribe' and verify_token == expected_verify_token:
        if challenge:
            logger.info(f"Webhook verification successful. Returning challenge: {challenge}")
            return PlainTextResponse(content=challenge, status_code=200)
        else:
            logger.error("Webhook verification error: Challenge not provided by Instagram.")
            raise HTTPException(status_code=400, detail="Challenge not provided by Instagram.")

    else:
        logger.error(f"Webhook verification failed. Mode: '{mode}', Received Token: '{verify_token}', Expected Token: '{expected_verify_token}'")
        raise HTTPException(status_code=403, detail="Verify token mismatch or mode not supported.")


@router.post('/webhook')
async def handle_webhook(request: Request):
    """
    Endpoint to receive webhook notifications.
    Acknowledges receipt immediately and processes data in background.
    """
    webhook_data = await request.json()
    logger.info(f"Received webhook data: {webhook_data}")
    # process the message webhooks
    asyncio.create_task(process_webhook_message(webhook_data))
    # 4. Return 200 OK immediately
    return {"status": "received", "message": "Processing in background"}


# Metrics
@router.get("/audience_growth_trend", response_model=List[InstagramAudienceGrowthTrendPoint])
async def get_instagram_audience_growth_trend_route(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
    start: Optional[str] = Query(None, description="Start date (ISO format)"),
    end: Optional[str] = Query(None, description="End date (ISO format)"),
    limit: int = Query(1000, ge=1, le=10000, description="Max number of points"),
):
    """
    Fetch audience growth trend (followers over time) for the given organization.
    Supports optional start/end date filtering.
    """
    start_dt = datetime.fromisoformat(start) if start else None
    end_dt = datetime.fromisoformat(end) if end else None
    points = await get_instagram_audience_growth_trend(
        db_session=db_session,
        organisation_id=organisation_id,
        start=start_dt,
        end=end_dt,
        limit=limit,
    )
    return points


@router.get("/post_engagement_trend", response_model=List[InstagramPostEngagementTrendPoint])
async def get_instagram_post_engagement_trend_route(
    organisation_id: Annotated[str, Depends(verify_organization)],
    media_id: str = Query(..., description="Instagram media ID (post ID)"),
    db_session: AsyncSession = Depends(get_db),
    start: Optional[str] = Query(None, description="Start date (ISO format)"),
    end: Optional[str] = Query(None, description="End date (ISO format)"),
    limit: int = Query(1000, ge=1, le=10000, description="Max number of points"),
):
    """
    Fetch post engagement trend (engagement over time) for a given post/media.
    Supports optional start/end date filtering.
    """
    start_dt = datetime.fromisoformat(start) if start else None
    end_dt = datetime.fromisoformat(end) if end else None
    points = await get_instagram_post_engagement_trend(
        db_session=db_session,
        organisation_id=organisation_id,
        media_id=media_id,
        start=start_dt,
        end=end_dt,
        limit=limit,
    )
    return points


@router.get("/engagement_trend", response_model=List[InstagramEngagementTrendPoint])
async def get_instagram_engagement_trend_route(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
    start: Optional[str] = Query(None, description="Start date (ISO format)"),
    end: Optional[str] = Query(None, description="End date (ISO format)"),
    limit: int = Query(1000, ge=1, le=10000, description="Max number of points"),
):
    """
    Fetch engagement trend (engagements over time) for the given organization.
    Supports optional start/end date filtering.
    """
    start_dt = datetime.fromisoformat(start) if start else None
    end_dt = datetime.fromisoformat(end) if end else None
    points = await get_instagram_engagement_trend(
        db_session=db_session,
        organisation_id=organisation_id,
        start=start_dt,
        end=end_dt,
        limit=limit,
    )
    return points


@router.get("/account_reach_trend", response_model=List[InstagramAccountReachTrendPoint])
async def get_instagram_account_reach_trend_route(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
    start: Optional[str] = Query(None, description="Start date (ISO format)"),
    end: Optional[str] = Query(None, description="End date (ISO format)"),
    limit: int = Query(1000, ge=1, le=10000, description="Max number of points"),
):
    """
    Fetch account reach trend (reach over time) for the given organization.
    Supports optional start/end date filtering.
    """
    start_dt = datetime.fromisoformat(start) if start else None
    end_dt = datetime.fromisoformat(end) if end else None
    points = await get_instagram_account_reach_trend(
        db_session=db_session,
        organisation_id=organisation_id,
        start=start_dt,
        end=end_dt,
        limit=limit,
    )
    return points


@router.get("/top_performing_post", response_model=List[InstagramTopPerformingPostSchema])
async def get_instagram_top_performing_post_route(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
    limit: int = Query(5, ge=1, le=20, description="Max number of posts"),
    order_by: str = Query("reach", description="Order by 'reach' or 'engagement'"),
):
    """
    Fetch top performing Instagram posts for the given organization, ordered by reach or engagement.
    """
    posts = await get_instagram_top_performing_posts(
        db_session=db_session,
        organisation_id=organisation_id,
        limit=limit,
        order_by=order_by,
    )
    return posts


@router.get("/overview_metrics", response_model=InstagramOverviewMetricsSchema)
async def get_instagram_overview_metrics_route(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """
    Fetch the latest Instagram overview metrics (followers, reach, engagements, etc.) for the given organization.
    """
    metrics = await get_instagram_overview_metrics(
        db_session=db_session,
        organisation_id=organisation_id,
    )
    if not metrics:
        raise HTTPException(status_code=404, detail="No overview metrics found")
    return success_response(200, "Account metrics returned", InstagramOverviewMetricsSchema.model_validate(metrics))


@router.get("/demographics", response_model=List[InstagramAudienceDemographicsSchema])
async def get_instagram_demographics(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
    breakdown_type: str = Query("country", description="Breakdown type: country, city, age, or gender"),
    limit: int = Query(45, ge=1, le=100, description="Max number of results"),
):
    """
    Fetch Instagram audience demographics for the given organization and breakdown type.
    The default breakdown is by country.
    """
    try:
        results = await get_instagram_audience_demographics(
            db_session=db_session,
            organisation_id=organisation_id,
            breakdown_type=breakdown_type,
            limit=limit,
        )
        # Convert ORM objects to schema objects
        return [InstagramAudienceDemographicsSchema.model_validate(obj) for obj in results]
    except Exception as e:
        logger.error(f"Error in /demographics route: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch demographics")
