"""
Transformers package for normalizing social media data structures.

This package provides transformation functions to convert platform-specific
data formats into unified, standardized structures.
"""

from .comment_transformer import (
    transform_instagram_comment_to_unified,
    transform_facebook_comment_to_unified,
    transform_comments_to_unified,
    transform_database_comment_to_unified,
    create_unified_comment_response,
    detect_platform_from_comment_data,
    batch_transform_comments,
)

__all__ = [
    "transform_instagram_comment_to_unified",
    "transform_facebook_comment_to_unified", 
    "transform_comments_to_unified",
    "transform_database_comment_to_unified",
    "create_unified_comment_response",
    "detect_platform_from_comment_data",
    "batch_transform_comments",
]
