import asyncio
import json
from datetime import datetime, timedelta
from typing import Annotated

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi import Depends, FastAPI, WebSocket, WebSocketDisconnect, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

from app.core.config import settings
from app.database.session import create_db_and_tables
from app.routes import router as socials_router
from app.services.websocket import (
    MessageEventType,
    register_connection,
    send_websocket_message,
    unregister_connection,
)
from app.tasks.scheduler import (
    check_and_post_scheduled_content,
    scheduled_metrics_update,
)
from app.utils.dependency import get_current_user_from_websocket, verify_organization
from app.utils.logger import get_logger
from app.utils.redis_cache import redis_client

logger = get_logger(__name__)

app = FastAPI(
    openapi_url="/api/v1/socials/openapi.json", docs_url="/api/v1/socials/docs"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Create the database tables
@app.on_event("startup")
async def on_startup():
    # Create database tables
    logger.info("Creating database tables...")
    try:
        await create_db_and_tables()
        logger.info("Database tables created successfully.")
    except Exception as e:
        logger.error(f"Error during database setup: {e}")


@app.on_event("startup")
async def setup_schedulers():
    app.state.scheduler = AsyncIOScheduler()

    # Schedule content posting
    app.state.scheduler.add_job(
        check_and_post_scheduled_content, "interval",
        seconds=settings.SCHEDULE_PERIOD
    )

    # Schedule metrics updates for all platforms
    # This will run the unified metrics update function that handles all platforms
    app.state.scheduler.add_job(
        scheduled_metrics_update, "interval", minutes=10,
        next_run_time=datetime.now() + timedelta(minutes=1)
        # Start first run after 5 minutes
    )

    app.state.scheduler.start()


@app.on_event("shutdown")
async def shutdown_apscheduler():
    app.state.scheduler.shutdown()


# Custom OpenAPI schema to include bearer token
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    # Base OpenAPI schema generation
    openapi_schema = get_openapi(
        title="KIMEV",
        version="1.0.0",
        description="Do cool AI Stuffs",
        routes=app.routes,
    )

    # Define multiple security schemes
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"},
    }
    for path, methods in openapi_schema["paths"].items():
        for method, details in methods.items():
            # Add or update security
            if "security" in details:
                details["security"].append({"Bearer": []})
            else:
                details["security"] = [{"Bearer": []}]

            # Ensure endpoint-level tags take precedence
            if "tags" in details and len(details["tags"]) > 1:
                if "Schedule" in details["tags"]:
                    details["tags"] = ["Schedule"]
                else:
                    details["tags"] = details["tags"][:1]

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

app.include_router(
    socials_router,
    prefix="/api/v1/socials",
)


@app.get("/socials_status")
def root_status():
    """
    This is just an introduction to this server
    requests to this endpoint is to confirm if the server is up and running
    """
    return {"message": "Welcome to the socials Service"}


@app.websocket("/ws/messages")
async def messages_ws(
    websocket: WebSocket,
    organisation_id: Annotated[str, Depends(verify_organization)],
    user: Annotated[dict, Depends(get_current_user_from_websocket)]
):
    """
    Enhanced WebSocket endpoint for real-time messaging with heartbeat support
    """
    user_id = user.get("user_id")
    user_email = user.get("email", "unknown")

    if not user_id:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        logger.info("WebSocket connection rejected: invalid token")
        return

    await websocket.accept()
    register_connection(organisation_id, user_id, websocket)
    logger.info(
        f"WebSocket connected: user {user_email} (org: {organisation_id})"
    )

    # Send connection confirmation
    await send_websocket_message(
        organization_id=organisation_id,
        event=MessageEventType.CONNECTION_STATUS,
        data={
            "status": "connected",
            "user_id": user_id,
            "user_email": user_email,
            "organization_id": organisation_id
        }
    )

    try:
        while True:
            try:
                data = await asyncio.wait_for(
                    websocket.receive_text(),
                    timeout=30.0
                )

                # Handle different message types
                try:
                    message = json.loads(data)
                    message_type = message.get("type", "unknown")

                    if message_type == "ping":
                        await websocket.send_json({
                            "type": "pong",
                            "timestamp": datetime.utcnow().isoformat()
                        })
                    elif message_type == "heartbeat":
                        # Acknowledge heartbeat
                        await websocket.send_json({
                            "type": "heartbeat_ack",
                            "timestamp": datetime.utcnow().isoformat()
                        })
                    else:
                        logger.info(
                            f"Received message from WS ({user_email}): "
                            f"{message_type}"
                        )

                except json.JSONDecodeError:
                    logger.info(
                        f"Received text from WS ({user_email}): {data[:100]}"
                    )

            except asyncio.TimeoutError:
                try:
                    await websocket.send_json({
                        "type": "ping",
                        "timestamp": datetime.utcnow().isoformat()
                    })
                except Exception:
                    break

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected: user {user_email}")
    except Exception as e:
        logger.error(f"WebSocket error for user {user_email}: {str(e)}")
    finally:
        unregister_connection(organisation_id, user_id)
        logger.info(f"WebSocket cleanup completed for user {user_email}")


@app.get("/redis-test")
async def redis_test():
    if not redis_client:
        return {"error": "Redis is not connected"}
    redis_client.set("test_key", "test_value")
    value = redis_client.get("test_key")
    return {"test_key": value}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="127.0.0.1", port=8001)
