"""
Unified comment schema for normalizing Instagram and Facebook comment data structures.

This module provides a standardized comment format that consolidates all comment-related
data from both Instagram and Facebook APIs into a single, consistent structure.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Union, Any
from pydantic import BaseModel, Field


class PlatformType(str, Enum):
    """Supported social media platforms"""
    INSTAGRAM = "instagram"
    FACEBOOK = "facebook"


class MediaType(str, Enum):
    """Types of media that can be attached to comments"""
    IMAGE = "image"
    VIDEO = "video"
    CAROUSEL_ALBUM = "carousel_album"
    STORY = "story"
    REEL = "reel"
    UNKNOWN = "unknown"


class ReactionType(str, Enum):
    """Types of reactions available on comments"""
    LIKE = "like"
    LOVE = "love"
    HAHA = "haha"
    WOW = "wow"
    SAD = "sad"
    ANGRY = "angry"
    CARE = "care"
    PRIDE = "pride"
    THANKFUL = "thankful"


class UnifiedCommentAuthor(BaseModel):
    """Unified author information for a comment"""
    id: str = Field(..., description="Unique identifier for the comment author")
    username: Optional[str] = Field(None, description="Username/display name of the author")
    name: Optional[str] = Field(None, description="Full name of the author (Facebook)")
    profile_picture_url: Optional[str] = Field(None, description="URL to author's profile picture")
    
    class Config:
        from_attributes = True


class UnifiedCommentMedia(BaseModel):
    """Unified media information attached to a comment"""
    id: Optional[str] = Field(None, description="Media ID")
    media_type: MediaType = Field(MediaType.UNKNOWN, description="Type of media")
    media_url: Optional[str] = Field(None, description="URL to the media file")
    thumbnail_url: Optional[str] = Field(None, description="URL to media thumbnail")
    alt_text: Optional[str] = Field(None, description="Alternative text for accessibility")
    caption: Optional[str] = Field(None, description="Media caption")
    
    class Config:
        from_attributes = True


class UnifiedCommentAttachment(BaseModel):
    """Unified attachment information for comments (Facebook-specific)"""
    attachment_type: Optional[str] = Field(None, description="Type of attachment")
    url: Optional[str] = Field(None, description="URL to the attachment")
    title: Optional[str] = Field(None, description="Attachment title")
    description: Optional[str] = Field(None, description="Attachment description")
    
    class Config:
        from_attributes = True


class UnifiedCommentReaction(BaseModel):
    """Unified reaction information for a comment"""
    reaction_type: ReactionType = Field(..., description="Type of reaction")
    user_id: Optional[str] = Field(None, description="ID of user who reacted")
    username: Optional[str] = Field(None, description="Username of user who reacted")
    name: Optional[str] = Field(None, description="Full name of user who reacted")
    
    class Config:
        from_attributes = True


class UnifiedCommentReply(BaseModel):
    """Unified reply structure for comment replies"""
    id: str = Field(..., description="Unique identifier for the reply")
    content: str = Field(..., description="Text content of the reply")
    created_time: datetime = Field(..., description="When the reply was created")
    author: UnifiedCommentAuthor = Field(..., description="Author of the reply")
    like_count: int = Field(0, description="Number of likes on the reply")
    media: Optional[UnifiedCommentMedia] = Field(None, description="Media attached to reply")
    platform: PlatformType = Field(..., description="Platform where reply originated")
    
    class Config:
        from_attributes = True


class UnifiedComment(BaseModel):
    """
    Unified comment structure that consolidates Instagram and Facebook comment data.
    
    This schema includes all available fields from both platforms to ensure no data loss
    and provides a consistent interface for frontend components.
    """
    # Core comment fields (available on both platforms)
    id: str = Field(..., description="Unique identifier for the comment")
    content: str = Field(..., description="Text content of the comment")
    created_time: datetime = Field(..., description="When the comment was created")
    author: UnifiedCommentAuthor = Field(..., description="Author of the comment")
    platform: PlatformType = Field(..., description="Platform where comment originated")
    
    # Engagement metrics
    like_count: int = Field(0, description="Number of likes on the comment")
    reply_count: int = Field(0, description="Number of replies to the comment")
    
    # Hierarchical structure
    parent_id: Optional[str] = Field(None, description="ID of parent comment if this is a reply")
    replies: List[UnifiedCommentReply] = Field(default_factory=list, description="Direct replies to this comment")
    
    # Media and attachments
    media: Optional[UnifiedCommentMedia] = Field(None, description="Media attached to comment (Instagram)")
    attachment: Optional[UnifiedCommentAttachment] = Field(None, description="Attachment info (Facebook)")
    
    # Reactions and interactions
    reactions: List[UnifiedCommentReaction] = Field(default_factory=list, description="Reactions to the comment")
    likes: List[Dict[str, Any]] = Field(default_factory=list, description="Detailed like information (Facebook)")
    
    # Platform-specific metadata
    is_hidden: Optional[bool] = Field(None, description="Whether comment is hidden (Facebook)")
    is_private: Optional[bool] = Field(None, description="Whether comment is private")
    
    # Additional metadata and raw data
    extra_data: Dict[str, Any] = Field(default_factory=dict, description="Platform-specific additional data")
    raw_data: Optional[Dict[str, Any]] = Field(None, description="Original raw API response for debugging")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "18067688915055635",
                "content": "This is a great post!",
                "created_time": "2025-05-27T17:33:45+0000",
                "author": {
                    "id": "17841453087020874",
                    "username": "john_doe",
                    "name": "John Doe"
                },
                "platform": "instagram",
                "like_count": 5,
                "reply_count": 2,
                "parent_id": None,
                "replies": [],
                "media": {
                    "id": "18082306612685122",
                    "media_type": "image",
                    "media_url": "https://example.com/image.jpg",
                    "thumbnail_url": "https://example.com/thumb.jpg"
                },
                "reactions": [],
                "is_hidden": False,
                "extra_data": {}
            }
        }


class UnifiedCommentResponse(BaseModel):
    """Unified response structure for comment endpoints"""
    comments: List[UnifiedComment] = Field(..., description="List of comments")
    total: int = Field(..., description="Total number of comments available")
    limit: int = Field(..., description="Number of comments per page")
    offset: int = Field(..., description="Offset for pagination")
    platform: Optional[PlatformType] = Field(None, description="Platform filter applied")
    
    class Config:
        from_attributes = True


class UnifiedCommentCreateRequest(BaseModel):
    """Unified request structure for creating comments"""
    content: str = Field(..., description="Text content of the comment", min_length=1)
    parent_id: Optional[str] = Field(None, description="ID of parent comment if replying")
    
    class Config:
        json_schema_extra = {
            "example": {
                "content": "This is a comment",
                "parent_id": None
            }
        }


class UnifiedCommentCreateResponse(BaseModel):
    """Unified response structure for comment creation"""
    id: str = Field(..., description="ID of the created comment")
    platform: PlatformType = Field(..., description="Platform where comment was created")
    success: bool = Field(..., description="Whether the comment was created successfully")
    message: str = Field(..., description="Success or error message")
    
    class Config:
        from_attributes = True
