import asyncio
import logging
from datetime import datetime
from sqlalchemy.future import select
from app.models import model
from app.database.session import SessionLocal
from app.services.twitter_service import TwitterService
from app.utils.redis_cache import redis_client

logger = logging.getLogger(__name__)

async def update_competitor_metrics(days: int = 30):
    """
    Update metrics for all competitors in the database
    This function is meant to be called by a scheduler
    """
    logger.info("Starting scheduled metrics update for all competitors")
    twitter_service = TwitterService()
    
    async with SessionLocal() as db:
        try:
            # Get all competitors
            result = await db.execute(
                select(model.TwitterCompetitor)
            )
            competitors = result.scalars().all()
            
            logger.info(f"Found {len(competitors)} competitors to update")
            
            for competitor in competitors:
                try:
                    # Get profile data
                    profile = await twitter_service.get_user_profile(competitor.username)
                    
                    # Get engagement metrics
                    engagement = await twitter_service.get_engagement_metrics(competitor.username, days=days)
                    
                    # Create metrics record
                    metrics = model.TwitterCompetitorMetrics(
                        competitor_id=competitor.id,
                        followers_count=profile.public_metrics.get('followers_count', 0),
                        following_count=profile.public_metrics.get('following_count', 0),
                        tweet_count=profile.public_metrics.get('tweet_count', 0),
                        listed_count=profile.public_metrics.get('listed_count', 0),
                        avg_likes=engagement['avg_likes'],
                        avg_retweets=engagement['avg_retweets'],
                        avg_replies=engagement['avg_replies'],
                        avg_quotes=engagement['avg_quotes'],
                        engagement_rate=engagement['engagement_rate'],
                        avg_tweet_length=engagement['content_metrics']['avg_tweet_length'],
                        media_percentage=engagement['content_metrics']['media_percentage'],
                        url_percentage=engagement['content_metrics']['url_percentage'],
                        hashtag_percentage=engagement['content_metrics']['hashtag_percentage']
                    )
                    
                    # Update competitor's last_metrics_update timestamp
                    competitor.last_metrics_update = datetime.now()
                    
                    # Add metrics to database
                    db.add(metrics)
                    
                    # Cache metrics in Redis
                    cache_key = f"competitor_metrics:{competitor.id}"
                    cache_data = {
                        "id": competitor.id,
                        "username": competitor.username,
                        "followers_count": profile.public_metrics.get('followers_count', 0),
                        "following_count": profile.public_metrics.get('following_count', 0),
                        "tweet_count": profile.public_metrics.get('tweet_count', 0),
                        "engagement_rate": engagement['engagement_rate'],
                        "updated_at": datetime.now().isoformat()
                    }
                    redis_client.set(cache_key, str(cache_data), ex=86400)  # Cache for 24 hours
                    
                    logger.info(f"Updated metrics for competitor: {competitor.username}")
                    
                    # Sleep to avoid rate limiting
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"Error updating metrics for competitor {competitor.username}: {str(e)}")
                    continue
            
            # Commit all changes
            await db.commit()
            logger.info("Completed scheduled metrics update for all competitors")
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error in scheduled metrics update: {str(e)}")

# Function to be called by the scheduler
async def scheduled_metrics_update():
    """
    Function to be called by the scheduler to update metrics for all competitors
    """
    await update_competitor_metrics()
