##################
# Dockerfile for FastAPI app

FROM python:3.12

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    libtesseract-dev \
    gcc \
    supervisor \
    build-essential \
    && apt-get clean

# Create and set working directory
WORKDIR /app

# Copy the requirements file and install dependencies
COPY ./requirements.txt /app/requirements.txt

RUN pip install --no-cache-dir -r /app/requirements.txt

COPY . /app

COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

RUN playwright install
RUN playwright install-deps

EXPOSE 8001
EXPOSE 5556

# Run the FastAPI app using Uvicorn
CMD ["supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
